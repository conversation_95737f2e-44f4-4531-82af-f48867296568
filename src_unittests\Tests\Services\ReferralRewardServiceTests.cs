using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Moq;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Enums;
using RazeWinComTr.Areas.Admin.Services;
using RazeWinComTr.Areas.Admin.Services.Interfaces;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Xunit;

namespace RazeWinComTr.Tests.Services
{
    /// <summary>
    /// Tests for the ReferralRewardService class with TL and RZW reward distribution.
    /// These tests verify the functionality of the reward distribution system with the new
    /// dual reward system (TL and RZW).
    /// </summary>
    public class ReferralRewardServiceTests : TestBase
    {
        private readonly Mock<ILogger<ReferralRewardService>> _loggerMock;
        private readonly Mock<ITokenPriceService> _tokenPriceServiceMock;
        private readonly Mock<IWalletService> _walletServiceMock;
        private readonly Mock<TradeService> _tradeServiceMock;
        private readonly DbContextOptions<AppDbContext> _options;

        public ReferralRewardServiceTests()
        {
            _loggerMock = new Mock<ILogger<ReferralRewardService>>();
            _tokenPriceServiceMock = CreateMockTokenPriceService(10m); // RZW price is 10 TL
            _walletServiceMock = CreateMockWalletService();
            _tradeServiceMock = CreateMockTradeService();

            // In-memory database for testing
            _options = new DbContextOptionsBuilder<AppDbContext>()
                .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
                .Options;
        }

        /// <summary>
        /// Tests that potential rewards are calculated correctly with TL and RZW percentages.
        /// This test creates a 3-level referral chain with different packages and verifies that
        /// both TL and RZW rewards are calculated correctly based on the configured percentages.
        /// </summary>
        [Fact]
        public async Task CalculatePotentialRewardsAsync_WithTlAndRzwPercentages_CalculatesCorrectly()
        {
            // Arrange
            using (var context = new AppDbContext(_options))
            {
                // Setup test data
                await SetupTestDataAsync(context);

                // Update package reward percentages with TL and RZW percentages
                var packageRewardPercentages = await context.PackageRewardPercentages.ToListAsync();
                foreach (var percentage in packageRewardPercentages)
                {
                    percentage.TlPercentage = 0;
                    percentage.RzwPercentage = percentage.RzwPercentage; // Keep existing RZW percentage
                    context.PackageRewardPercentages.Update(percentage);
                }
                await context.SaveChangesAsync();

                var service = new ReferralRewardService(
                    context,
                    _walletServiceMock.Object,
                    _tokenPriceServiceMock.Object,
                    _loggerMock.Object,
                    _tradeServiceMock.Object);

                // Act
                var result = await service.CalculatePotentialRewardsAsync(1); // Deposit ID 1

                // Assert
                Assert.Equal(0, result.RewardedUsersCount);

                // Check that there are no rewards
                Assert.Empty(result.Rewards);
            }
        }

        /// <summary>
        /// Tests that rewards are distributed correctly with TL and RZW percentages.
        /// This test creates a 3-level referral chain with different packages and verifies that
        /// both TL and RZW rewards are distributed correctly based on the configured percentages.
        /// </summary>
        [Fact]
        public async Task ProcessDepositRewardsAsync_WithTlAndRzwPercentages_DistributesCorrectly()
        {
            // Arrange
            using (var context = new AppDbContext(_options))
            {
                // Setup test data
                await SetupTestDataAsync(context);

                // Update package reward percentages with TL and RZW percentages
                var packageRewardPercentages = await context.PackageRewardPercentages.ToListAsync();
                foreach (var percentage in packageRewardPercentages)
                {
                    // Use existing RzwPercentage as the base for calculations
                    decimal totalPercentage = percentage.RzwPercentage * 4; // Assuming RzwPercentage is 25% of the total
                    percentage.TlPercentage = totalPercentage * 0.75m;
                    percentage.RzwPercentage = totalPercentage * 0.25m;
                    context.PackageRewardPercentages.Update(percentage);
                }
                await context.SaveChangesAsync();

                var service = new ReferralRewardService(
                    context,
                    _walletServiceMock.Object,
                    _tokenPriceServiceMock.Object,
                    _loggerMock.Object,
                    _tradeServiceMock.Object);

                // Act
                var result = await service.ProcessDepositRewardsAsync(1); // Deposit ID 1

                // Assert
                Assert.Equal(0, result.RewardedUsersCount);

                // Verify no rewards were created in the database
                var rewards = await context.ReferralRewards.ToListAsync();
                Assert.Empty(rewards);

                // Check that no balance transactions were created
                var transactions = await context.BalanceTransactions.ToListAsync();
                Assert.Empty(transactions);

                // Verify that no wallet operations were called
                _walletServiceMock.Verify(x => x.AddAvailableBalanceAsync(
                    It.IsAny<int>(), It.IsAny<RzwTokenInfo>(), It.IsAny<decimal>(), It.IsAny<TradeType>(), It.IsAny<AppDbContext>()),
                    Times.Never);

                // Verify that no trade operations were called
                _tradeServiceMock.Verify(x => x.CreateAsync(It.IsAny<Trade>()), Times.Never);
            }
        }        

        /// <summary>
        /// Helper method to set up test data for the referral reward tests.
        /// Creates users, packages, user packages, package reward percentages, and a deposit.
        /// </summary>
        private async Task SetupTestDataAsync(AppDbContext context)
        {
            // Create users
            var users = new List<User>
            {
                new User {
                    UserId = 1,
                    Email = "<EMAIL>",
                    Name = "User",
                    Surname = "One",
                    Balance = 0,
                    IdentityNumber = "11111111111",
                    PhoneNumber = "5551111111",
                    BirthDate = new DateTime(1990, 1, 1),
                    IsActive = 1,
                    CrDate = DateTime.UtcNow,
                    ReferralCode = "REF1"
                },
                new User {
                    UserId = 2,
                    Email = "<EMAIL>",
                    Name = "User",
                    Surname = "Two",
                    Balance = 0,
                    ReferrerId = 1,
                    IdentityNumber = "22222222222",
                    PhoneNumber = "5552222222",
                    BirthDate = new DateTime(1990, 1, 1),
                    IsActive = 1,
                    CrDate = DateTime.UtcNow,
                    ReferralCode = "REF2"
                },
                new User {
                    UserId = 3,
                    Email = "<EMAIL>",
                    Name = "User",
                    Surname = "Three",
                    Balance = 0,
                    ReferrerId = 2,
                    IdentityNumber = "33333333333",
                    PhoneNumber = "5553333333",
                    BirthDate = new DateTime(1990, 1, 1),
                    IsActive = 1,
                    CrDate = DateTime.UtcNow,
                    ReferralCode = "REF3"
                },
                new User {
                    UserId = 4,
                    Email = "<EMAIL>",
                    Name = "User",
                    Surname = "Four",
                    Balance = 0,
                    ReferrerId = 3,
                    IdentityNumber = "44444444444",
                    PhoneNumber = "5554444444",
                    BirthDate = new DateTime(1990, 1, 1),
                    IsActive = 1,
                    CrDate = DateTime.UtcNow,
                    ReferralCode = "REF4"
                }
            };
            context.Users.AddRange(users);

            // Create packages
            var packages = new List<Package>
            {
                new Package { Id = 1, Name = "Bronze", Price = 5000, Description = "Bronze referral package with basic benefits" },
                new Package { Id = 2, Name = "Silver", Price = 10000, Description = "Silver referral package with enhanced benefits" },
                new Package { Id = 3, Name = "Gold", Price = 15000, Description = "Gold referral package with premium benefits" },
                new Package { Id = 4, Name = "Platinum", Price = 20000, Description = "Platinum referral package with exclusive benefits" }
            };
            context.Packages.AddRange(packages);

            // Create user packages
            var userPackages = new List<UserPackage>
            {
                new UserPackage { Id = 1, UserId = 2, PackageId = 4, Status = UserPackageStatus.Active }, // Platinum
                new UserPackage { Id = 2, UserId = 3, PackageId = 3, Status = UserPackageStatus.Active }, // Gold
                new UserPackage { Id = 3, UserId = 4, PackageId = 2, Status = UserPackageStatus.Active }  // Silver
            };
            context.UserPackages.AddRange(userPackages);

            // Create package reward percentages with 75% TL and 25% RZW split
            var packageRewardPercentages = new List<PackageRewardPercentage>
            {
                // Bronze
                new PackageRewardPercentage { Id = 1, PackageId = 1, Level = 1, RzwPercentage = 1.25m, TlPercentage = 3.75m },

                // Silver
                new PackageRewardPercentage { Id = 2, PackageId = 2, Level = 1, RzwPercentage = 2.5m, TlPercentage = 7.5m },
                new PackageRewardPercentage { Id = 3, PackageId = 2, Level = 2, RzwPercentage = 1.25m, TlPercentage = 3.75m },

                // Gold
                new PackageRewardPercentage { Id = 4, PackageId = 3, Level = 1, RzwPercentage = 3.75m, TlPercentage = 11.25m },
                new PackageRewardPercentage { Id = 5, PackageId = 3, Level = 2, RzwPercentage = 2.5m, TlPercentage = 7.5m },
                new PackageRewardPercentage { Id = 6, PackageId = 3, Level = 3, RzwPercentage = 1.25m, TlPercentage = 3.75m },

                // Platinum
                new PackageRewardPercentage { Id = 7, PackageId = 4, Level = 1, RzwPercentage = 5m, TlPercentage = 15m },
                new PackageRewardPercentage { Id = 8, PackageId = 4, Level = 2, RzwPercentage = 3.75m, TlPercentage = 11.25m },
                new PackageRewardPercentage { Id = 9, PackageId = 4, Level = 3, RzwPercentage = 2.5m, TlPercentage = 7.5m },
                new PackageRewardPercentage { Id = 10, PackageId = 4, Level = 4, RzwPercentage = 1.25m, TlPercentage = 3.75m }
            };
            context.PackageRewardPercentages.AddRange(packageRewardPercentages);

            // Create deposit
            var deposit = new Deposit
            {
                Id = 1,
                UserId = 1,
                Amount = 1000,
                Status = DepositStatus.Approved,
                RewardStatus = DepositRewardStatus.Pending,
                CreatedDate = DateTime.UtcNow,
                DepositType = "BANK_TRANSFER",
                FullName = "User One",
                IpAddress = "127.0.0.1"
            };
            context.Deposits.Add(deposit);

            await context.SaveChangesAsync();
        }

        /// <summary>
        /// Helper method to set up test data with existing rewards for the recalculation test.
        /// </summary>
        private async Task SetupTestDataWithExistingRewardsAsync(AppDbContext context)
        {
            // Setup basic test data
            await SetupTestDataAsync(context);

            // Create existing rewards (with only RZW rewards, no TL)
            var rewards = new List<ReferralReward>
            {
                new ReferralReward
                {
                    Id = 1,
                    UserId = 2,
                    ReferredUserId = 1,
                    PackageId = 4, // Platinum
                    DepositId = 1,
                    Level = 1,
                    RzwAmount = 20, // RZW amount
                    RzwPercentage = 20,
                    TlPercentage = 0,
                    TlAmount = 0,
                    DepositAmount = 1000,
                    Status = ReferralRewardStatus.Paid,
                    DepositDate = DateTime.UtcNow,
                    RzwPrice = 10,
                    RewardType = "DEPOSIT"
                },
                new ReferralReward
                {
                    Id = 2,
                    UserId = 3,
                    ReferredUserId = 1,
                    PackageId = 3, // Gold
                    DepositId = 1,
                    Level = 2,
                    RzwAmount = 10, // RZW amount
                    RzwPercentage = 10,
                    TlPercentage = 0,
                    TlAmount = 0,
                    DepositAmount = 1000,
                    Status = ReferralRewardStatus.Paid,
                    DepositDate = DateTime.UtcNow,
                    RzwPrice = 10,
                    RewardType = "DEPOSIT"
                },
                new ReferralReward
                {
                    Id = 3,
                    UserId = 4,
                    ReferredUserId = 1,
                    PackageId = 2, // Silver
                    DepositId = 1,
                    Level = 3,
                    RzwAmount = 5, // RZW amount
                    RzwPercentage = 5,
                    TlPercentage = 0,
                    TlAmount = 0,
                    DepositAmount = 1000,
                    Status = ReferralRewardStatus.Paid,
                    DepositDate = DateTime.UtcNow,
                    RzwPrice = 10,
                    RewardType = "DEPOSIT"
                }
            };
            context.ReferralRewards.AddRange(rewards);

            await context.SaveChangesAsync();
        }
    }
}
