using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Diagnostics;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using Moq;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Services;
using RazeWinComTr.Areas.Admin.Services.Interfaces;

namespace RazeWinComTr.Tests
{
    /// <summary>
    /// Base class for all test classes in the RazeWinComTr.Tests namespace.
    /// Provides common functionality for creating test database contexts, mock services, and loggers.
    /// </summary>
    public abstract class TestBase
    {
        /// <summary>
        /// Creates a new in-memory database context for testing.
        /// Each test should use a unique database name to ensure test isolation.
        /// </summary>
        /// <param name="databaseName">A unique name for the in-memory database</param>
        /// <returns>A new AppDbContext instance connected to an in-memory database</returns>
        protected AppDbContext CreateDbContext(string databaseName)
        {
            var options = new DbContextOptionsBuilder<AppDbContext>()
                .UseInMemoryDatabase(databaseName)
                .ConfigureWarnings(w => w.Ignore(InMemoryEventId.TransactionIgnoredWarning))
                .Options;

            var context = new AppDbContext(options);
            context.Database.EnsureDeleted();
            context.Database.EnsureCreated();
            return context;
        }

        /// <summary>
        /// Creates a mock logger for testing.
        /// </summary>
        /// <typeparam name="T">The type that the logger is for</typeparam>
        /// <returns>A mock ILogger instance</returns>
        protected Mock<ILogger<T>> CreateMockLogger<T>()
        {
            return new Mock<ILogger<T>>();
        }



        /// <summary>
        /// Creates a mock TokenPriceService for testing using Moq.
        /// The mock is configured to return the specified RZW price for all RZW price-related calls.
        /// </summary>
        /// <param name="buyPrice">The RZW price to return (default: 1.0)</param>
        /// <param name="sellPrice">The RZW sell price to return (default: buyPrice * 0.95)</param>
        /// <param name="coinId">The coin ID to return (default: 1)</param>
        /// <returns>A Mock&lt;ITokenPriceService&gt; instance</returns>
        protected Mock<ITokenPriceService> CreateMockTokenPriceService(decimal buyPrice = 1.0m, decimal? sellPrice = null, int coinId = 1)
        {
            var actualSellPrice = sellPrice ?? buyPrice * 0.95m;
            var mock = new Mock<ITokenPriceService>();

            var rzwTokenInfo = new RzwTokenInfo
            {
                TokenId = coinId,
                BuyPrice = buyPrice,
                SellPrice = actualSellPrice
            };

            mock.Setup(x => x.GetCurrentRzwBuyPriceAsync())
                .ReturnsAsync(buyPrice);

            mock.Setup(x => x.GetRzwTokenIdAsync())
                .ReturnsAsync(coinId);

            mock.Setup(x => x.GetRzwTokenInfoAsync())
                .ReturnsAsync(rzwTokenInfo);

            mock.Setup(x => x.GetCoinInfoAsync(coinId))
                .ReturnsAsync(rzwTokenInfo);

            return mock;
        }

        /// <summary>
        /// Creates a mock TradeService for testing using Moq.
        /// </summary>
        /// <returns>A Mock&lt;TradeService&gt; instance</returns>
        protected Mock<TradeService> CreateMockTradeService()
        {
            var mockLocalizer = new Mock<IStringLocalizer<SharedResource>>();
            var mockContext = new Mock<AppDbContext>(new DbContextOptions<AppDbContext>());

            var mock = new Mock<TradeService>(mockLocalizer.Object, mockContext.Object);

            // Setup default behavior for CreateAsync
            mock.Setup(x => x.CreateAsync(It.IsAny<Trade>()))
                .ReturnsAsync((Trade trade) =>
                {
                    trade.Id = new Random().Next(1, 1000);
                    return trade;
                });

            return mock;
        }

        /// <summary>
        /// Creates a mock WalletService for testing using Moq.
        /// </summary>
        /// <returns>A Mock&lt;IWalletService&gt; instance</returns>
        protected Mock<IWalletService> CreateMockWalletService()
        {
            var mock = new Mock<IWalletService>();

            // Setup default behavior for AddAvailableBalanceAsync
            mock.Setup(x => x.AddAvailableBalanceAsync(It.IsAny<int>(), It.IsAny<RzwTokenInfo>(), It.IsAny<decimal>(), It.IsAny<TradeType>(), It.IsAny<AppDbContext>()))
                .ReturnsAsync((int userId, RzwTokenInfo tokenInfo, decimal amount, TradeType tradeType, AppDbContext context) =>
                    new Wallet { Id = 1, UserId = userId, CoinId = tokenInfo.TokenId, Balance = amount });

            // Setup default behavior for GetByUserIdAndCoinIdAsync
            mock.Setup(x => x.GetByUserIdAndCoinIdAsync(It.IsAny<int>(), It.IsAny<int>(), It.IsAny<AppDbContext>()))
                .ReturnsAsync((Wallet?)null);

            return mock;
        }

        /// <summary>
        /// Creates a mock WalletService with verification capabilities for testing.
        /// </summary>
        /// <returns>A Mock&lt;IWalletService&gt; instance with verification methods</returns>
        protected MockWalletServiceWrapper CreateMockWalletServiceWithVerification()
        {
            var mock = CreateMockWalletService();
            return new MockWalletServiceWrapper(mock);
        }

        /// <summary>
        /// Creates a mock TradeService with verification capabilities for testing.
        /// </summary>
        /// <returns>A Mock&lt;TradeService&gt; instance with verification methods</returns>
        protected MockTradeServiceWrapper CreateMockTradeServiceWithVerification()
        {
            var mock = CreateMockTradeService();
            return new MockTradeServiceWrapper(mock);
        }
    }

    /// <summary>
    /// Wrapper class to provide verification capabilities for mocked WalletService
    /// </summary>
    public class MockWalletServiceWrapper
    {
        private readonly Mock<IWalletService> _mock;
        private readonly List<(int UserId, int CoinId, decimal Amount)> _addToWalletCalls = new();

        public MockWalletServiceWrapper(Mock<IWalletService> mock)
        {
            _mock = mock;

            // Setup to track calls
            _mock.Setup(x => x.AddAvailableBalanceAsync(It.IsAny<int>(), It.IsAny<RzwTokenInfo>(), It.IsAny<decimal>(), It.IsAny<TradeType>(), It.IsAny<AppDbContext>()))
                .ReturnsAsync((int userId, RzwTokenInfo tokenInfo, decimal amount, TradeType tradeType, AppDbContext context) =>
                {
                    _addToWalletCalls.Add((userId, tokenInfo.TokenId, amount));
                    return new Wallet { Id = 1, UserId = userId, CoinId = tokenInfo.TokenId, Balance = amount };
                });
        }

        public Mock<IWalletService> Object => _mock;
        public int AddToWalletAsyncCallCount => _addToWalletCalls.Count;
        public List<(int UserId, int CoinId, decimal Amount)> AddToWalletAsyncCalls => _addToWalletCalls;

        public bool VerifyAddToWalletAsync(int userId, int coinId, decimal amount, int times = 1, decimal tolerance = 0.01m)
        {
            return _addToWalletCalls.Count(call =>
                call.UserId == userId &&
                call.CoinId == coinId &&
                Math.Abs(call.Amount - amount) <= tolerance) == times;
        }
    }

    /// <summary>
    /// Wrapper class to provide verification capabilities for mocked TradeService
    /// </summary>
    public class MockTradeServiceWrapper
    {
        private readonly Mock<TradeService> _mock;
        private int _createAsyncCallCount = 0;

        public MockTradeServiceWrapper(Mock<TradeService> mock)
        {
            _mock = mock;

            // Setup to track calls
            _mock.Setup(x => x.CreateAsync(It.IsAny<Trade>()))
                .ReturnsAsync((Trade trade) =>
                {
                    _createAsyncCallCount++;
                    trade.Id = new Random().Next(1, 1000);
                    return trade;
                });
        }

        public Mock<TradeService> Object => _mock;
        public int CreateAsyncCallCount => _createAsyncCallCount;
    }
}
